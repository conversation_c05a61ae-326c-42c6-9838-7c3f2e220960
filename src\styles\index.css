:root {
  --default-font-size: 14px;
  --clr-primary: #b45434;
  --clr-secondary: #252829;
  --clr-text: #252829;
  --clr-light: #f5f7f7;
}

/*fontsize mixin for html*/
@font-face {
  font-family: "29LTBukra-Light";
  src: url("../assets/fonts/29LTBukra-Light.woff2") format("woff2"),
    url("../assets/fonts/29LTBukra-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "29LTBukra-Regular";
  src: url("../assets/fonts/29LTBukra-Regular.woff2") format("woff2"),
    url("../assets/fonts/29LTBukra-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "29LTBukra-Medium";
  src: url("../assets/fonts/29LTBukra-Medium.woff2") format("woff2"),
    url("../assets/fonts/29LTBukra-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "29LTBukra-Bold";
  src: url("../assets/fonts/29LTBukra-Bold.woff2") format("woff2"),
    url("../assets/fonts/29LTBukra-Bold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
a {
  text-decoration: none;
  color: inherit;
}
html {
  accent-color: var(--clr-primary);
  scroll-behavior: smooth;
  font-size: 80%;
}
@media screen and (min-width: 576px) {
  html {
    font-size: 80%;
  }
}
@media screen and (min-width: 768px) {
  html {
    font-size: 80%;
  }
}
@media screen and (min-width: 1400px) {
  html {
    font-size: 0.9vw;
  }
}
@media screen and (min-width: 1920px) {
  html {
    font-size: 0.8vw;
  }
}
@media screen and (min-width: 3000px) {
  html {
    font-size: 0.7vw;
  }
}

body {
  font: 1rem "29LTBukra-Regular", Calibri, Arial, sans-serif;
  line-height: 2;
  background-color: var(--clr-secondary);
}
body::-webkit-scrollbar {
  width: 0.875rem;
  height: 0.875rem;
}
body::-webkit-scrollbar-thumb {
  background: #b45434;
  border-radius: 0;
}
body::-webkit-scrollbar-track {
  background: #f5f7f7;
  border-radius: 0;
}
body {
  scrollbar-face-color: #b45434;
  scrollbar-track-color: #f5f7f7;
}

::-moz-selection {
  background-color: var(--clr-primary);
  color: #fff;
}

::selection {
  background-color: var(--clr-primary);
  color: #fff;
}

.container-fluid {
  padding-inline: 7rem;
}
@media (max-width: 1600px) {
  .container-fluid {
    padding-inline: 4rem;
  }
}
@media (max-width: 1400px) {
  .container-fluid {
    padding-inline: 2rem;
  }
}
@media (min-width: 1600px) {
  .container-fluid {
    max-width: 87%;
  }
}

.text-muted {
  color: #586665;
}

section.padding-block {
  padding: 5rem 0;
}

h1,
h2,
h3,
h4,
h5 {
  font-family: "29LTBukra-Bold", Calibri, Arial, sans-serif;
  line-height: 1.7;
}

.list-unstyled {
  padding: 0;
}

.back-top-btn {
  position: fixed;
  right: 2.5rem;
  inset-block-end: 2.5rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  /* opacity: 0; */
  cursor: pointer;
  visibility: hidden;
  width: 2.8125rem;
  height: 2.8125rem;
  border-radius: 50%;
  background-color: var(--clr-secondary);
  z-index: 99;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.back-top-btn svg {
  width: 1.25rem;
  height: 1.25rem;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.back-top-btn:hover {
  background-color: var(--clr-primary);
}
.back-top-btn:hover svg {
  -webkit-transform: translateY(-4px);
  -ms-transform: translateY(-4px);
  transform: translateY(-4px);
}

#preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  z-index: 10000;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
#preloader .preloader-content {
  z-index: 2;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: opacity 0.6s ease;
  transition: opacity 0.6s ease;
}
#preloader .preloader-content .logo {
  position: relative;
  margin-bottom: 20px;
}
#preloader .preloader-content .logo img {
  height: 6.25rem;
}
#preloader .preloader-content h1 {
  font-size: 1.5rem;
}
#preloader .preloader-content.fade-out {
  opacity: 0;
  -webkit-transform: scale(0.95);
  -ms-transform: scale(0.95);
  transform: scale(0.95);
}
#preloader .slice {
  position: absolute;
  top: 0;
  width: 50%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  -webkit-transition: -webkit-transform 0.8s ease;
  transition: -webkit-transform 0.8s ease;
  transition: transform 0.8s ease;
  transition: transform 0.8s ease, -webkit-transform 0.8s ease;
  z-index: 1;
}
#preloader .slice.slice1 {
  left: 0;
}
#preloader .slice.slice2 {
  left: 50%;
}
#preloader .slice.slide1 {
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transform: translateX(-100%);
}
#preloader .slice.slide2 {
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
}

.fade-out {
  -webkit-animation: fadeOut 1s;
  animation: fadeOut 1s;
  opacity: 0;
  visibility: hidden;
}

@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
    visibility: visible;
  }
  to {
    opacity: 0;
    visibility: hidden;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    visibility: visible;
  }
  to {
    opacity: 0;
    visibility: hidden;
  }
}
.section-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 3rem;
}
.section-header .section-title {
  font-size: 2.5rem;
  font-family: "29LTBukra-Medium";
  color: var(--clr-secondary);
  margin-bottom: 0;
}
@media (max-width: 1199.98px) {
  .section-header .section-title {
    font-size: 1.5rem;
  }
}
.section-header .section-title .colored {
  color: var(--clr-primary);
}
.section-header .section-description {
  /* line-height: 2; */
  line-height: 2.5;
  font-size: 1.125rem;
}
.section-header.text-white .section-title {
  color: #fff;
}
.section-header.text-white .section-description {
  color: #d9d9d9;
}
@media (max-width: 767.98px) {
  .section-header {
    margin-bottom: 1.5rem;
  }
}

.custom-tooltip {
  --bs-tooltip-bg: var(--bs-white);
  --bs-tooltip-color: var(--clr-text);
  font-family: "29LTBukra-Regular";
  font-size: 0.875rem;
}

.primary-tooltip {
  --bs-tooltip-bg: var(--clr-primary);
  --bs-tooltip-color: var(--bs-white);
  font-family: "29LTBukra-Regular";
  font-size: 0.875rem;
}

@-webkit-keyframes floatY {
  0% {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  100% {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}

@keyframes floatY {
  0% {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  100% {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}
@-webkit-keyframes floatX {
  0% {
    -webkit-transform: translateX(0px);
    transform: translateX(0px);
  }
  50% {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }
  100% {
    -webkit-transform: translateX(0px);
    transform: translateX(0px);
  }
}
@keyframes floatX {
  0% {
    -webkit-transform: translateX(0px);
    transform: translateX(0px);
  }
  50% {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }
  100% {
    -webkit-transform: translateX(0px);
    transform: translateX(0px);
  }
}
@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.07);
    transform: scale(1.07);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes pulse {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.07);
    transform: scale(1.07);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
.floting-menu {
  position: relative;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  margin-block: auto;
  /* padding: 1.5rem 2rem 1.5rem 1rem; */
  /* padding: 1.5rem 2rem 1.5rem 0; */
  border-radius: 1.5rem;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}
.floting-menu .floting-menu-toggle {
  position: absolute;
  inset-inline-start: -1rem;
  inset-block-start: 1rem;
  display: none;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 2rem;
  width: 2rem;
  isolation: isolate;
  border: 0;
  padding: 0;
  border-radius: 2rem;
  background-color: var(--clr-primary);
  z-index: 3;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.floting-menu .floting-menu-toggle svg {
  height: 1rem;
  width: 1rem;
  -o-object-fit: contain;
  object-fit: contain;
}
.floting-menu .floting-menu-toggle:hover {
  -webkit-transform: scale(1.12);
  -ms-transform: scale(1.12);
  transform: scale(1.12);
}
.floting-menu .floting-menu-toggle:hover::before {
  background-color: var(--clr-primary);
}
.floting-menu .menu-title {
  color: #fff;
  line-height: 1.7;
  font-family: "29LTBukra-Medium";
  margin-bottom: 1.5rem;
  text-align: center;
}
.floting-menu__body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 0;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  max-height: 100vh;
  /* -webkit-padding-end: 1rem;
  padding-inline-end: 1rem; */
  /* overflow-y: auto; */
  overflow-x: clip;
  padding-top: 0.5rem;
}
.floting-menu__body::-webkit-scrollbar {
  width: 0.5rem;
  height: 0.5rem;
}
.floting-menu__body::-webkit-scrollbar-thumb {
  background: #b45434;
  border-radius: 0;
}
.floting-menu__body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.4509803922);
  border-radius: 0;
}
.floting-menu__body {
  scrollbar-face-color: #b45434;
  scrollbar-track-color: rgba(0, 0, 0, 0.4509803922);
}
.floting-menu__body::-webkit-scrollbar-track,
.floting-menu__body::-webkit-scrollbar-thumb {
  border-radius: 2rem;
}
.floting-menu__body .floting-menu-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  padding: 1.125rem;
  border-radius: 1.5rem;
  background-color: rgba(0, 0, 0, 0.5);
  text-decoration: none;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}
.floting-menu__body .floting-menu-item__header {
  border-bottom: 1px solid #fff;
  padding-bottom: 0.75rem;
  margin-bottom: 1.25rem;
}
.floting-menu__body .floting-menu-item__header .item-year {
  font-family: "29LTBukra-Medium";
  color: #fff;
}
.floting-menu__body .floting-menu-item__body .item-chart {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: #fff;
}
.floting-menu__body .floting-menu-item__body .item-chart .chart-container {
  height: 3.75rem;
  width: 3.75rem;
}
.floting-menu__body .floting-menu-item:hover,
.floting-menu__body .floting-menu-item.active {
  /* background-color: #fff; */
  -webkit-transform: scale(1.02) translateY(-5px);
  -ms-transform: scale(1.02) translateY(-5px);
  transform: scale(1.02) translateY(-5px);
  /* -webkit-box-shadow: 0 4px 24px rgba(180, 84, 52, 0.24); */
  /* box-shadow: 0 4px 24px rgba(180, 84, 52, 0.24); */
}
.floting-menu__body .floting-menu-item:hover .floting-menu-item__header,
.floting-menu__body .floting-menu-item.active .floting-menu-item__header {
  border-bottom-color: var(--clr-primary);
}
.floting-menu__body .floting-menu-item:hover .item-year,
.floting-menu__body .floting-menu-item.active .item-year {
  color: var(--clr-primary);
}
.floting-menu__body .floting-menu-item:hover .item-chart,
.floting-menu__body .floting-menu-item.active .item-chart {
  /* color: var(--clr-text); */
}
@media (max-width: 767.98px) {
  .floting-menu__body .menu-item a {
    width: 100%;
  }
}
@media (max-width: 991.98px) {
  .floting-menu__body {
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
  }
}

.page-banner {
  position: relative;
  padding-block: 5rem;
  z-index: 1;
}
.page-banner .bg {
  position: absolute;
  inset: 0;
  background-color: var(--clr-secondary);
  isolation: isolate;
}
.page-banner .bg img {
  position: relative;
  inset-inline-end: 0;
  mix-blend-mode: overlay;
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: left;
  object-position: left;
  z-index: -1;
}
.page-banner .bg::before {
  position: absolute;
  inset: 0;
  content: "";
  background-color: rgba(42, 80, 77, 0.86);
  mix-blend-mode: overlay;
  z-index: 0;
}
.page-banner .bg::after {
  position: absolute;
  inset: 0;
  content: "";
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    from(rgba(27, 51, 49, 0)),
    color-stop(45%, #182e2c)
  );
  background: linear-gradient(90deg, rgba(27, 51, 49, 0) 0%, #182e2c 45%);
  z-index: 1;
}
.page-banner__content {
  position: relative;
  z-index: 2;
}
.page-banner .page-title {
  position: relative;
  font-size: 3.625rem;
  font-family: "29LTBukra-Bold";
  color: #fff;
  margin-bottom: 1rem;
}
.page-banner .page-description {
  position: relative;
  color: #98a6a5;
  font-size: 1.125rem;
  line-height: 2;
  margin-bottom: 0;
}
@media (min-width: 1200px) {
  .page-banner .page-description {
    max-width: 46%;
  }
}

.pagination-main {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 1rem;
}
.pagination-main .page-size {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.5rem;
}
.pagination-main .page-size .title {
  white-space: nowrap;
}
.pagination-main .page-size select {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  min-width: 4rem;
}

.pagination {
  --bs-pagination-padding-x: 0.75rem;
  --bs-pagination-padding-y: 0.75rem;
  --bs-pagination-font-size: 1rem;
  --bs-pagination-color: var(--clr-primary);
  --bs-pagination-bg: transparent;
  --bs-pagination-border-width: var(--bs-border-width);
  --bs-pagination-border-color: var(--clr-primary);
  --bs-pagination-border-radius: 0;
  --bs-pagination-hover-color: #fff;
  --bs-pagination-hover-bg: var(--clr-primary);
  --bs-pagination-hover-border-color: var(--clr-primary);
  --bs-pagination-focus-color: #fff;
  --bs-pagination-focus-bg: var(--clr-primary);
  --bs-pagination-focus-box-shadow: none;
  --bs-pagination-active-color: #fff;
  --bs-pagination-active-bg: var(--clr-primary);
  --bs-pagination-active-border-color: var(--clr-primary);
  gap: 0.5rem;
}
.pagination .page-item a {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 3rem;
  text-align: center;
}
.pagination .page-item a:hover svg path,
.pagination .page-item a.active svg path {
  fill: #fff;
}
.pagination .page-item.disabled {
  display: none;
}

.breadcrumb {
  --bs-breadcrumb-divider: url("data:image/svg+xml,%3Csvg width='16' height='12' viewBox='0 0 16 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.469669 5.46967C0.176776 5.76256 0.176776 6.23744 0.469669 6.53033L5.24264 11.3033C5.53553 11.5962 6.01041 11.5962 6.3033 11.3033C6.59619 11.0104 6.59619 10.5355 6.3033 10.2426L2.06066 6L6.3033 1.75736C6.59619 1.46447 6.59619 0.989592 6.3033 0.696698C6.01041 0.403805 5.53553 0.403805 5.24264 0.696698L0.469669 5.46967ZM16 5.25L0.999999 5.25L0.999999 6.75L16 6.75L16 5.25Z' fill='%2398A6A5'/%3E%3C/svg%3E%0A");
  --bs-breadcrumb-item-active-color: #e2e8e8;
  --bs-breadcrumb-item-padding-x: 0.75rem;
  --bs-breadcrumb-padding-x: 0;
  --bs-breadcrumb-padding-y: 0;
  --bs-breadcrumb-margin-bottom: 2rem;
}
.breadcrumb .breadcrumb-item a {
  text-decoration: none;
  color: #e2e8e8;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.breadcrumb .breadcrumb-item a:hover {
  color: var(--clr-primary);
}

.s4-breadcrumb-arrowcont,
a.ms-breadcrumbRootNode {
  display: none;
}

ul.breadcrumb-item {
  padding: 0;
  margin: 0;
}
ul.breadcrumb-item::before {
  display: none;
}

ul.ms-breadcrumbRootNode {
  padding: 0;
  margin: 0;
}
ul.ms-breadcrumbRootNode::before {
  display: none;
}
ul.ms-breadcrumbRootNode > .ms-breadcrumbRootNode {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.details-page .item-image {
  position: relative;
  min-height: 40vh;
  margin-bottom: 2rem;
  overflow: hidden;
}
.swiper .details-page .item-image {
  height: 31.25rem;
  min-height: unset;
  overflow: hidden;
}
.details-page .item-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
.details-page .item-title {
  font-size: 2rem;
  font-family: "29LTBukra-Bold";
  margin-bottom: 0.5rem;
  line-height: 1.5;
}
.details-page .item-description {
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
}

.swiper .item-image {
  height: 37.5rem;
  min-height: unset;
  overflow: hidden;
}

@media (max-width: 1399.98px) {
  .aside-widgets-container {
    -webkit-padding-before: 3.125rem;
    padding-block-start: 3.125rem;
  }
}
@media (max-width: 575.98px) {
  .aside-widgets-container {
    -webkit-padding-before: 7.5rem;
    padding-block-start: 7.5rem;
  }
}

.font-dropdown {
  margin-bottom: 1.5rem;
}
.font-dropdown .dropdown-toggle {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.5rem;
  background-color: transparent;
  border: 1px solid #e2e8e8;
  padding: 0.5rem 1rem;
  border-radius: 0;
}
.font-dropdown .dropdown-toggle:after {
  width: 1rem;
  height: 0.5rem;
  border: 0;
  -webkit-margin-start: auto;
  margin-inline-start: auto;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='8' viewBox='0 0 12 8' fill='none'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M11.7614 1.98352L6.57614 7.16874C6.25795 7.48693 5.74206 7.48693 5.42386 7.16874L0.238644 1.98352C-0.0795479 1.66533 -0.0795479 1.14944 0.238644 0.831249C0.556834 0.513058 1.07272 0.513058 1.39091 0.831249L6 5.44033L10.6091 0.831249C10.9273 0.513058 11.4432 0.513058 11.7614 0.831249C12.0795 1.14944 12.0795 1.66533 11.7614 1.98352Z' fill='%23586665'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M11.7614 1.98352L6.57614 7.16874C6.25795 7.48693 5.74206 7.48693 5.42386 7.16874L0.238644 1.98352C-0.0795479 1.66533 -0.0795479 1.14944 0.238644 0.831249C0.556834 0.513058 1.07272 0.513058 1.39091 0.831249L6 5.44033L10.6091 0.831249C10.9273 0.513058 11.4432 0.513058 11.7614 0.831249C12.0795 1.14944 12.0795 1.66533 11.7614 1.98352Z' fill='black' fill-opacity='0.05'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 0.75rem;
}
.font-dropdown .dropdown-toggle .title {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  border-bottom: 0.1875rem solid var(--clr-primary);
  line-height: 1;
  gap: 0.125rem;
  font-size: 1.125rem;
  color: var(--clr-secondary);
}
.font-dropdown .dropdown-toggle .title .lg {
  font-size: 1.5rem;
}
.font-dropdown .dropdown-menu {
  --bs-dropdown-zindex: 1000;
  --bs-dropdown-min-width: 10rem;
  --bs-dropdown-padding-x: 1rem;
  --bs-dropdown-padding-y: 1rem;
  --bs-dropdown-spacer: 0.125rem;
  --bs-dropdown-font-size: 1rem;
  --bs-dropdown-color: #586665;
  --bs-dropdown-bg: #fff;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-border-radius: 0;
  --bs-dropdown-border-width: 0;
  --bs-dropdown-inner-border-radius: 0;
  --bs-dropdown-divider-bg: transparent;
  --bs-dropdown-divider-margin-y: 0.125rem;
  --bs-dropdown-link-color: #586665;
  --bs-dropdown-link-hover-color: var(--clr-primary);
  --bs-dropdown-link-hover-bg: transparent;
  --bs-dropdown-link-active-color: #fff;
  --bs-dropdown-link-active-bg: transparent;
  --bs-dropdown-link-disabled-color: #ddd;
  --bs-dropdown-item-padding-x: 0.5rem;
  --bs-dropdown-item-padding-y: 0.5rem;
  --bs-dropdown-header-padding-x: 1rem;
  -webkit-box-shadow: 0px 12px 20px 0px rgba(0, 0, 0, 0.15);
  box-shadow: 0px 12px 20px 0px rgba(0, 0, 0, 0.15);
}
.font-dropdown .dropdown-menu .dropdown-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.font-dropdown .dropdown-menu .dropdown-item.font-sm {
  font-size: 0.875rem;
}
.font-dropdown .dropdown-menu .dropdown-item.font-lg {
  font-size: 1.5rem;
}
.font-dropdown .dropdown-menu .dropdown-item.selected::after {
  content: "";
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 0.86125rem;
  height: 0.86125rem;
  -webkit-margin-start: auto;
  margin-inline-start: auto;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='15' height='12' viewBox='0 0 15 12' fill='none'%3E%3Cpath d='M13.8084 2L5.80837 10L2 6.19169' stroke='%2300A05A' stroke-width='2.38496' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-position: center;
  background-size: contain;
}
.font-dropdown .dropdown-menu .dropdown-item:hover {
  color: var(--clr-primary) !important;
}
.font-dropdown .dropdown-menu.show {
  margin-top: -0.25rem !important;
}

.gallery-slider {
  position: relative;
  padding-inline: 2rem;
  overflow: hidden !important;
}

.accordion {
  --bs-accordion-color: var(--clr-text);
  --bs-accordion-bg: #fff;
  --bs-accordion-transition: color 0.15s ease-in-out,
    background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  --bs-accordion-border-color: var(--bs-border-color);
  --bs-accordion-border-width: 0;
  --bs-accordion-border-radius: 0;
  --bs-accordion-inner-border-radius: 0;
  --bs-accordion-btn-padding-x: 1.25rem;
  --bs-accordion-btn-padding-y: 1rem;
  --bs-accordion-btn-color: var(--clr-text);
  --bs-accordion-btn-bg: #f5f7f7;
  --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%23212529' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M2 5L8 11L14 5'/%3e%3c/svg%3e");
  --bs-accordion-btn-icon-width: 1.25rem;
  --bs-accordion-btn-icon-transform: rotate(-180deg);
  --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%23052c65' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M2 5L8 11L14 5'/%3e%3c/svg%3e");
  --bs-accordion-btn-focus-box-shadow: none;
  --bs-accordion-body-padding-x: 1.25rem;
  --bs-accordion-body-padding-y: 1rem;
  --bs-accordion-active-color: var(--clr-text);
  --bs-accordion-active-bg: #f5f7f7;
}
.accordion .accordion-item {
  margin-bottom: 0.75rem;
}
.accordion .accordion-item .accordion-button {
  -webkit-border-start: 4px solid transparent;
  border-inline-start: 4px solid transparent;
}
.accordion .accordion-item .accordion-button[aria-expanded="true"] {
  border-inline-start-color: var(--clr-primary);
}

.btn {
  padding: 0.75rem 2rem;
  font-family: "29LTBukra-Medium";
  border-radius: 5rem;
  font-size: 1rem;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 1199.98px) {
  .btn {
    padding: 0.5rem 1.5rem;
    font-size: 0.875rem;
    font-family: "29LTBukra-Medium";
  }
}

.btn-primary {
  --bs-btn-color: #fff;
  --bs-btn-bg: var(--clr-primary);
  --bs-btn-border-color: var(--clr-primary);
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #da7652;
  --bs-btn-hover-border-color: #da7652;
  --bs-btn-focus-shadow-rgb: 0;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #da7652;
  --bs-btn-active-border-color: #da7652;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: var(--clr-primary);
  --bs-btn-disabled-border-color: var(--clr-primary);
}

.btn-secondary {
  --bs-btn-color: #fff;
  --bs-btn-bg: var(--clr-secondary);
  --bs-btn-border-color: var(--clr-secondary);
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #da7652;
  --bs-btn-hover-border-color: #da7652;
  --bs-btn-focus-shadow-rgb: 0;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #da7652;
  --bs-btn-active-border-color: #da7652;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: var(--clr-secondary);
  --bs-btn-disabled-border-color: var(--clr-secondary);
}

.btn-outline-secondary {
  --bs-btn-color: var(--clr-secondary);
  --bs-btn-bg: transparent;
  --bs-btn-border-color: var(--clr-secondary);
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: var(--clr-primary);
  --bs-btn-hover-border-color: var(--clr-primary);
  --bs-btn-focus-shadow-rgb: 0;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: var(--clr-primary);
  --bs-btn-active-border-color: var(--clr-secondary);
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: var(--clr-secondary);
  padding: 1rem 2rem;
  font-family: "29LTBukra-Medium";
  border-radius: 5rem;
}

.btn-outline-white {
  --bs-btn-color: #fff;
  --bs-btn-bg: transparent;
  --bs-btn-border-color: #fff;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: var(--clr-primary);
  --bs-btn-hover-border-color: var(--clr-primary);
  --bs-btn-focus-shadow-rgb: 0;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: var(--clr-primary);
  --bs-btn-active-border-color: var(--clr-primary);
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #fff;
  padding: 1rem 2rem;
  font-family: "29LTBukra-Medium";
  border-radius: 5rem;
}

.main-header .dropdown .dropdown-toggle,
.offcanvas .dropdown .dropdown-toggle {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.5rem;
}
.main-header .dropdown .dropdown-toggle:after,
.offcanvas .dropdown .dropdown-toggle:after {
  width: 1rem;
  height: 0.5rem;
  border: 0;
  -webkit-margin-start: auto;
  margin-inline-start: auto;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='8' viewBox='0 0 12 8' fill='none'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M11.7614 1.98352L6.57614 7.16874C6.25795 7.48693 5.74206 7.48693 5.42386 7.16874L0.238644 1.98352C-0.0795479 1.66533 -0.0795479 1.14944 0.238644 0.831249C0.556834 0.513058 1.07272 0.513058 1.39091 0.831249L6 5.44033L10.6091 0.831249C10.9273 0.513058 11.4432 0.513058 11.7614 0.831249C12.0795 1.14944 12.0795 1.66533 11.7614 1.98352Z' fill='%23586665'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M11.7614 1.98352L6.57614 7.16874C6.25795 7.48693 5.74206 7.48693 5.42386 7.16874L0.238644 1.98352C-0.0795479 1.66533 -0.0795479 1.14944 0.238644 0.831249C0.556834 0.513058 1.07272 0.513058 1.39091 0.831249L6 5.44033L10.6091 0.831249C10.9273 0.513058 11.4432 0.513058 11.7614 0.831249C12.0795 1.14944 12.0795 1.66533 11.7614 1.98352Z' fill='black' fill-opacity='0.05'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 0.75rem;
}
.main-header .dropdown .dropdown-menu,
.offcanvas .dropdown .dropdown-menu {
  --bs-dropdown-zindex: 1000;
  --bs-dropdown-min-width: 10rem;
  --bs-dropdown-padding-x: 1rem;
  --bs-dropdown-padding-y: 1rem;
  --bs-dropdown-spacer: 0.125rem;
  --bs-dropdown-font-size: 1rem;
  --bs-dropdown-color: #fff;
  --bs-dropdown-bg: rgba(0, 0, 0, 0.4);
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-border-radius: 0;
  --bs-dropdown-border-width: 0;
  --bs-dropdown-inner-border-radius: 0;
  --bs-dropdown-divider-bg: #fff;
  --bs-dropdown-divider-margin-y: 0.5rem;
  --bs-dropdown-link-color: #fff;
  --bs-dropdown-link-hover-color: var(--clr-primary);
  --bs-dropdown-link-hover-bg: transparent;
  --bs-dropdown-link-active-color: #fff;
  --bs-dropdown-link-active-bg: transparent;
  --bs-dropdown-link-disabled-color: #ddd;
  --bs-dropdown-item-padding-x: 1rem;
  --bs-dropdown-item-padding-y: 1rem;
  --bs-dropdown-header-padding-x: 1rem;
  border-top: 4px solid var(--clr-primary);
  -webkit-backdrop-filter: blur(12.5px);
  backdrop-filter: blur(12.5px);
  -webkit-box-shadow: 0px 36px 60px -24px rgba(0, 160, 90, 0.3);
  box-shadow: 0px 36px 60px -24px rgba(0, 160, 90, 0.3);
}
.main-header .dropdown .dropdown-menu li,
.offcanvas .dropdown .dropdown-menu li {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.main-header .dropdown .dropdown-menu li:not(:last-child),
.offcanvas .dropdown .dropdown-menu li:not(:last-child) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.main-header .dropdown .dropdown-menu li:hover,
.offcanvas .dropdown .dropdown-menu li:hover {
  border-bottom-color: var(--clr-primary);
}
.main-header .dropdown .dropdown-menu .dropdown-item,
.offcanvas .dropdown .dropdown-menu .dropdown-item {
  color: #fff;
}
.main-header .dropdown .dropdown-menu .dropdown-item:hover,
.offcanvas .dropdown .dropdown-menu .dropdown-item:hover {
  color: var(--clr-primary) !important;
}
.main-header .dropdown .dropdown-menu.show,
.offcanvas .dropdown .dropdown-menu.show {
  margin-top: -0.25rem !important;
  top: auto !important;
}
@media (hover) {
  .main-header .dropdown:hover .dropdown-menu,
  .offcanvas .dropdown:hover .dropdown-menu {
    margin-top: -0.25rem !important;
    display: block;
  }
}

.navbar-nav {
  gap: 1.5rem;
}
.navbar-nav .nav-item .nav-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 0.25rem solid transparent;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.navbar-nav .nav-item .nav-link.active {
  font-family: "29LTBukra-Medium";
  color: var(--clr-primary);
  border-bottom-color: var(--clr-primary);
}
.navbar-nav .nav-item .nav-link:hover {
  color: var(--clr-primary);
  border-bottom-color: var(--clr-primary);
}

.main-header {
  position: fixed;
  z-index: 99;
  top: 0;
  inset-inline: 0;
  background-color: transparent;
  -webkit-transition: all 0.3s ease-in-out !important;
  transition: all 0.3s ease-in-out !important;
}
.main-header .navbar .navbar-brand {
  padding: 1.5rem 0;
}
.main-header .navbar .navbar-brand img {
  height: 3.75rem;
  -webkit-filter: brightness(0) invert(1);
  filter: brightness(0) invert(1);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 767.98px) {
  .main-header .navbar .navbar-brand {
    padding-top: 0.5rem;
  }
  .main-header .navbar .navbar-brand img {
    height: 2.5rem;
  }
}
.main-header .navbar .logo-2030 {
  position: relative;
  padding-inline: 1.5rem;
  -webkit-border-start: 1px solid rgba(255, 255, 255, 0.3);
  border-inline-start: 1px solid rgba(255, 255, 255, 0.3);
}
.main-header .navbar .logo-2030 img {
  height: 2.8125rem;
  -webkit-filter: brightness(0) invert(1);
  filter: brightness(0) invert(1);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 767.98px) {
  .main-header .navbar .logo-2030 img {
    height: 1.875rem;
  }
}
.main-header .navbar .logo-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.5rem;
  /* -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1; */
}
.main-header .navbar .logo-container .btn-toggle {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: rgba(255, 255, 255, 0);
  border: 0;
  border-radius: 0.5rem;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.main-header .navbar .logo-container .btn-toggle:hover {
  background-color: var(--clr-primary);
  -webkit-transform: scale(1.05);
  -ms-transform: scale(1.05);
  transform: scale(1.05);
}
body:has(.offcanvas.show) .main-header .navbar .logo-container .btn-toggle {
  display: none !important;
}
@media (max-width: 575.98px) {
  .main-header .navbar .logo-container .btn-toggle {
    -webkit-margin-start: auto;
    margin-inline-start: auto;
  }
}
@media (max-width: 575.98px) {
  .main-header .navbar .logo-container {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
}
.main-header .navbar .navbar-nav .nav-item .nav-link {
  padding: 2.25rem 0;
  color: #fff;
}
.main-header .navbar .navbar-nav .nav-item .nav-link.active {
  font-family: "29LTBukra-Medium";
  border-bottom-color: #fff;
}
.main-header .navbar .navbar-nav .nav-item .nav-link:hover {
  color: #fff;
  border-bottom-color: #fff;
}
@media (max-width: 1400px) {
  .main-header .navbar .navbar-nav .nav-item .nav-link {
    font-size: 0.875rem;
  }
}
.main-header .header-actions {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  gap: 1rem;
  padding-block: 1rem;
}
.main-header .header-actions a,
.main-header .header-actions button {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.5rem;
  color: #fff;
  text-decoration: none;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  line-height: 1;
}
.main-header .header-actions a svg,
.main-header .header-actions button svg {
  height: 1rem;
  width: 1rem;
  -o-object-fit: contain;
  object-fit: contain;
}
.main-header .header-actions a svg path,
.main-header .header-actions button svg path {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.main-header .header-actions a:hover,
.main-header .header-actions button:hover {
  color: var(--clr-primary);
}
.main-header .header-actions a:hover svg path,
.main-header .header-actions button:hover svg path {
  fill: var(--clr-primary);
}
@media (max-width: 1400px) {
  .main-header .header-actions a,
  .main-header .header-actions button {
    font-size: 0.875rem;
  }
}
/* [dir="ltr"] .main-header .header-actions .login-btn svg {
  -webkit-transform: scale(-1);
  -ms-transform: scale(-1);
  transform: scale(-1);
} */
@media (max-width: 575.98px) {
  .main-header .header-actions {
    width: 100%;
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1;
  }
}
.main-header.scrolled {
  background-color: #fff;
  -webkit-box-shadow: 0px 36px 60px -24px rgba(121, 40, 2, 0.3);
  box-shadow: 0px 36px 60px -24px rgba(121, 40, 2, 0.3);
}
.main-header.scrolled .navbar-brand {
  padding: 0.25rem 0 1rem;
}
.main-header.scrolled .navbar-brand img {
  height: 3.125rem;
  -webkit-filter: none;
  filter: none;
}
.main-header.scrolled .logo-2030 {
  border-inline-start-color: rgba(0, 0, 0, 0.2);
}
.main-header.scrolled .logo-2030 img {
  height: 2.5rem;
  -webkit-filter: none;
  filter: none;
}
.main-header.scrolled .logo-container .btn-toggle svg path {
  fill: #000;
}
.main-header.scrolled .navbar-nav .nav-item .nav-link {
  color: var(--clr-text);
  padding-block: 1.5rem;
}
.main-header.scrolled .navbar-nav .nav-item .nav-link.active,
.main-header.scrolled .navbar-nav .nav-item .nav-link:hover {
  color: var(--clr-primary);
  border-bottom-color: var(--clr-primary);
}
.main-header.scrolled .header-actions {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  gap: 1rem;
}
.main-header.scrolled .header-actions a,
.main-header.scrolled .header-actions button {
  color: var(--clr-text);
}
.main-header.scrolled .header-actions a svg path,
.main-header.scrolled .header-actions button svg path {
  fill: var(--clr-text);
}
.main-header.scrolled .header-actions a:hover,
.main-header.scrolled .header-actions button:hover {
  color: var(--clr-primary);
}
.main-header.scrolled .header-actions a:hover svg path,
.main-header.scrolled .header-actions button:hover svg path {
  fill: var(--clr-primary);
}
body:has(.inner-page) .main-header {
  position: sticky;
}

.offcanvas .offcanvas-body::-webkit-scrollbar {
  width: 0.5rem;
  height: 0.5rem;
}
.offcanvas .offcanvas-body::-webkit-scrollbar-thumb {
  background: var(--clr-primary);
  border-radius: 0;
}
.offcanvas .offcanvas-body::-webkit-scrollbar-track {
  background: var(--clr-light);
  border-radius: 0;
}
.offcanvas .offcanvas-body {
  scrollbar-face-color: var(--clr-primary);
  scrollbar-track-color: var(--clr-light);
}
.offcanvas .dropdown .dropdown-menu {
  --bs-dropdown-bg: var(--clr-secondary);
  position: static !important;
  inset: 0 !important;
  margin: 0 !important;
  -webkit-transform: none !important;
  -ms-transform: none !important;
  transform: none !important;
}

.page-footer {
  position: relative;
  padding-top: 13rem;
  background-color: var(--clr-secondary);
  isolation: isolate;
}

/* Contact Us Section in carousel context */
.page-contact-us {
  position: relative;
  padding-top: 13rem;
  background-color: var(--clr-secondary);
  isolation: isolate;
}

.page-contact-us:before {
  position: absolute;
  inset: 0;
  content: "";
  opacity: 0.5;
  background-image: url("./assets/images//wave-pattern.svg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  mix-blend-mode: multiply;
  z-index: -1;
}

.home-vertical-swiper .page-contact-us {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 2rem;
  overflow-y: auto;
}

/* Footer Links Section in carousel context */
.page-footer-links {
  position: relative;
  padding-top: 13rem;
  background-color: var(--clr-secondary);
  isolation: isolate;
}

.page-footer-links:before {
  position: absolute;
  inset: 0;
  content: "";
  opacity: 0.5;
  background-image: url("./assets/images//wave-pattern.svg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  mix-blend-mode: multiply;
  z-index: -1;
}

.home-vertical-swiper .page-footer-links {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 2rem;
  overflow-y: auto;
}

.home-vertical-swiper .page-footer-links .footer {
  margin-top: 1rem;
  padding: 2rem 0 1rem;
}

/* Legacy footer styles for backward compatibility */
.home-vertical-swiper .page-footer {
  /* height: 100vh; */
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 2rem;
  overflow-y: auto;
}

.home-vertical-swiper .page-footer .mbile-app {
  flex-shrink: 0;
}

.home-vertical-swiper .page-footer .footer {
  margin-top: 1rem;
  padding: 2rem 0 1rem;
}
.page-footer:before {
  position: absolute;
  inset: 0;
  content: "";
  opacity: 0.5;
  background-image: url("./assets/images//wave-pattern.svg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  mix-blend-mode: multiply;
  z-index: -1;
}

.footer {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 4rem 0 2rem;
  margin-top: 3rem;
  gap: 1.5rem;
}
.footer::before {
  position: absolute;
  inset: 0;
  content: "";
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(rgba(217, 217, 217, 0)),
    color-stop(41.35%, rgba(0, 0, 0, 0)),
    to(#000)
  );
  background: linear-gradient(
    180deg,
    rgba(217, 217, 217, 0) 0%,
    rgba(0, 0, 0, 0) 41.35%,
    #000 100%
  );
  z-index: -1;
}
.footer .footer-logo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}
@media (max-width: 991.98px) {
  .footer .footer-logo {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
  }
}
.footer .footer-logo img {
  max-width: 100%;
}
.footer .footer-list-title {
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
  font-family: "29LTBukra-Regular";
  color: var(--clr-primary);
  color: #a9593c;
}
.footer .footer-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 0;
  margin: 0;
  list-style: none;
  gap: 0.5rem;
}
.footer .footer-list li {
  margin-bottom: 0.5rem;
}
.footer .footer-list li a {
  color: var(--clr-text-color);
  padding-block: 0;
  line-height: 1;
  text-decoration: none;
  color: #fff;
  font-size: ToRem(14px);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.footer .footer-list li a:is(:hover, :active, :focus) {
  color: var(--clr-primary);
}
@media (max-width: 575.98px) {
  .footer {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
  .footer .footer-list {
    margin-inline: auto;
  }
}

.social-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0;
  margin: 0;
  list-style: none;
}
.social-list li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  color: var(--clr-text-color);
  padding-block: 0;
  line-height: 1;
  text-decoration: none;
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--clr-primary);
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.social-list li a:is(:hover, :active, :focus) {
  color: var(--clr-primary);
}
.social-list li a img {
  height: 1rem;
}
.social-list li a:hover {
  -webkit-transform: scale(1.02) translateY(-2px);
  -ms-transform: scale(1.02) translateY(-2px);
  transform: scale(1.02) translateY(-2px);
}

.copyrights {
  padding-top: 1.25rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  color: #a9593c;
  position: relative;
  z-index: 10000;
}

.form-label {
  color: #586665;
  margin-bottom: 0.375rem;
}

.form-control,
.form-select {
  padding: 0.5rem 0.75rem;
  border-radius: 0;
  border: 1px solid #98a6a5;
  min-height: 3rem;
  line-height: 2rem;
}
.form-control:focus,
.form-select:focus {
  border-color: var(--clr-primary);
  -webkit-box-shadow: none;
  box-shadow: none;
}

.form-select {
  -webkit-padding-end: 2.5rem;
  padding-inline-end: 2.5rem;
}

.date-control {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='23.625' height='23.625' viewBox='0 0 23.625 23.625'%3E%3Cpath id='Path_140' data-name='Path 140' d='M21.94,23.062H3.564a1.875,1.875,0,0,1-1.875-1.875V4.687A1.875,1.875,0,0,1,3.564,2.812H21.94a1.875,1.875,0,0,1,1.875,1.875v16.5A1.875,1.875,0,0,1,21.94,23.062Z' transform='translate(-0.939 -0.187)' fill='none' stroke='%232a504d' stroke-miterlimit='10' stroke-width='1.5'/%3E%3Cpath id='Path_141' data-name='Path 141' d='M5.627,4.687V.937' transform='translate(-0.939 -0.187)' fill='none' stroke='%232a504d' stroke-linecap='square' stroke-miterlimit='10' stroke-width='1.5'/%3E%3Cpath id='Path_142' data-name='Path 142' d='M19.877.937v3.75' transform='translate(-0.939 -0.187)' fill='none' stroke='%232a504d' stroke-linecap='square' stroke-miterlimit='10' stroke-width='1.5'/%3E%3Cpath id='Path_143' data-name='Path 143' d='M20.252,19.218H16.5v-3.75h3.75Z' transform='translate(-0.939 -0.187)' fill='none' stroke='%2300a05a' stroke-miterlimit='10' stroke-width='1.5'/%3E%3Cpath id='Path_144' data-name='Path 144' d='M1.689,7.874H23.815' transform='translate(-0.939 -0.187)' fill='none' stroke='%232a504d' stroke-linecap='round' stroke-miterlimit='10' stroke-width='1.5'/%3E%3C/svg%3E%0A");
  background-repeat: no-repeat;
  background-size: 1.5rem;
  background-position-x: 0.75rem;
  background-position-y: center;
}

.card {
  --bs-card-spacer-y: 1rem;
  --bs-card-spacer-x: 0;
  --bs-card-title-spacer-y: 0.5rem;
  --bs-card-border-width: 0;
  --bs-card-border-radius: 0;
  --bs-card-inner-border-radius: 0;
  --bs-card-cap-padding-y: 0.5rem;
  --bs-card-cap-padding-x: 0;
  --bs-card-bg: transparent;
}
.card .card-img {
  position: relative;
  height: 15.625rem;
  overflow: hidden;
  background-color: var(--clr-secondary);
}
.card .card-img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.card .card-body .item-date {
  color: #586665;
  font-size: 0.875rem;
}
.card .card-body .card-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.5;
  overflow: hidden;
  word-wrap: break-word;
  text-overflow: ellipsis;
  font-size: 1.5rem;
  visibility: visible; /* this is a tricky to work correctly at IOS */
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
@supports not (-webkit-line-clamp: 2) {
  .card .card-body .card-title {
    display: block;
    max-height: 72px;
    line-height: 1.5;
  }
}
.card .card-body .card-link {
  color: var(--clr-primary);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.card .card-body .card-link:hover {
  opacity: 0.6;
}
.card:hover .card-img img {
  height: 15.625rem;
  -webkit-transform: scale(1.12);
  -ms-transform: scale(1.12);
  transform: scale(1.12);
}
.hero-caption-card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 1rem;
  color: #fff;
  padding-top: 8rem;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.hero-caption-card .caption-title {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  font-family: "29LTBukra-Bold";
  color: #fff;
  margin: 0;
  -webkit-transform: none !important;
  -ms-transform: none !important;
  transform: none !important;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: 2;
  overflow: hidden;
  word-wrap: break-word;
  text-overflow: ellipsis;
  font-size: 2rem;
  visibility: visible; /* this is a tricky to work correctly at IOS */
}
@supports not (-webkit-line-clamp: 3) {
  .hero-caption-card .caption-title {
    display: block;
    max-height: 192px;
    line-height: 2;
  }
}
@media (max-width: 1199.98px) {
  .hero-caption-card .caption-title {
    font-size: 1.5rem;
  }
}
.hero-caption-card .caption-sub-title {
  margin-bottom: 0;
  -webkit-transition-delay: 0.3s;
  transition-delay: 0.3s;
}
.hero-caption-card .caption-description {
  -webkit-transition-delay: 0.4s;
  transition-delay: 0.4s;
  font-family: "29LTBukra-Light";
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 2;
  overflow: hidden;
  word-wrap: break-word;
  text-overflow: ellipsis;
  font-size: 1.0625rem;
  visibility: visible; /* this is a tricky to work correctly at IOS */
}
@supports not (-webkit-line-clamp: 2) {
  .hero-caption-card .caption-description {
    display: block;
    max-height: 68px;
    line-height: 2;
  }
}
@media (max-width: 767.98px) {
  .hero-caption-card .caption-description {
    font-size: 1rem;
  }
}
.hero-caption-card .caption-actions {
  margin-bottom: 3rem;
  -webkit-transition-delay: 0.5s;
  transition-delay: 0.5s;
}
.hero-caption-card .caption-actions .title {
  min-width: 8.125rem;
}
@media (max-width: 991.98px) {
  .hero-caption-card .caption-actions {
    margin-bottom: 1rem;
  }
}
.hero-caption-card > * {
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateX(-40px);
  -ms-transform: translateX(-40px);
  transform: translateX(-40px);
  -webkit-transition: all 0.6s ease-in-out;
  transition: all 0.6s ease-in-out;
}
[dir="ltr"] .hero-caption-card > * {
  -webkit-transform: translateX(40px);
  -ms-transform: translateX(40px);
  transform: translateX(40px);
}
.swiper-slide-active .hero-caption-card > * {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translate(0);
  -ms-transform: translate(0);
  transform: translate(0);
}

/* Ensure proper slide visibility management */
.hero-slider .swiper-slide:not(.swiper-slide-active) .hero-caption-card > * {
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Only show active slide content */
.hero-slider .swiper-slide-active .hero-caption-card > * {
  opacity: 1 !important;
  visibility: visible !important;
  -webkit-transform: translate(0) !important;
  -ms-transform: translate(0) !important;
  transform: translate(0) !important;
}

/* Hide all slides initially until swiper is initialized */
.hero-slider:not(.swiper-initialized) .hero-caption-card > * {
  opacity: 0 !important;
  visibility: hidden !important;
}
@media (max-width: 1199.98px) {
  .hero-caption-card {
    padding-top: 1rem;
  }
}

.e-services-card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  padding: 3rem;
  border-radius: 1rem;
  min-height: 18.75rem;
  margin-block: 2rem;
  min-width: 11.25rem;
  background-color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  color: var(--clr-text);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.e-services-card .service-icon {
  margin-bottom: 1rem;
}
.e-services-card .service-icon img,
.e-services-card .service-icon svg {
  height: 4.0625rem;
}
.e-services-card .service-title {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    from(#000),
    to(#b45434)
  );
  background: linear-gradient(to right, #000, #b45434);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}
.e-services-card .service-description {
  font-size: 1.125rem;
  line-height: 1.7;
  font-family: "29LTBukra-Light";
}
@media (min-width: 992px) {
  .e-services-slider .swiper-slide-active .e-services-card {
    -webkit-transform: scale(1.12);
    -ms-transform: scale(1.12);
    transform: scale(1.12);
    background-color: var(--clr-primary);
  }
  .e-services-slider .swiper-slide-active .e-services-card .service-icon {
    -webkit-filter: brightness(0) invert(1);
    filter: brightness(0) invert(1);
  }
  .e-services-slider .swiper-slide-active .e-services-card .service-title,
  .e-services-slider
    .swiper-slide-active
    .e-services-card
    .service-description {
    color: #fff;
    background: none;
    -webkit-text-fill-color: #fff;
  }
}

.statistic-card,
.services-card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  background-color: rgb(255 255 255 / 70%);
  border-radius: 1.5rem;
  /* padding: 2.5rem 2rem; */
  padding: 1.5rem 2.5rem;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.services-card {
  padding: 3.5rem 2rem;
  height: 100%;
}
.statistic-card .statistic-icon img,
.services-card .services-icon img {
  height: 4.0625rem;
  width: 4.0625rem;
  margin-bottom: 1.5rem;
  -o-object-fit: contain;
  object-fit: contain;
}
.statistic-card .statistic-count {
  font-size: 2rem;
  font-family: "29LTBukra-Medium";
  color: var(--clr-primary);
}
.statistic-card:hover,
.services-card:hover {
  -webkit-transform: scale(1.02) translateY(-5px);
  -ms-transform: scale(1.02) translateY(-5px);
  transform: scale(1.02) translateY(-5px);
  -webkit-box-shadow: 0 4px 24px rgba(180, 84, 52, 0.24);
  box-shadow: 0 4px 24px rgba(180, 84, 52, 0.24);
}

.mbile-app-card {
  position: relative;
  padding: 3rem;
  background-color: rgba(0, 0, 0, 0.2);
}
@media (min-width: 992px) {
  .mbile-app-card .mobile-app-info {
    -webkit-padding-start: 4rem;
    padding-inline-start: 4rem;
  }
}
.mbile-app-card .mobile-app-info .title {
  font-size: 1.75rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  font-family: "29LTBukra-Bold";
  color: #fff;
}
.mbile-app-card .mobile-app-info .description {
  font-size: 1.125rem;
  line-height: 1.7;
  margin-bottom: 1rem;
  font-family: "29LTBukra-Light";
  color: #adadad;
  line-height: 2;
  margin-bottom: 2.5rem;
}
@media (min-width: 1200px) {
  .mbile-app-card .mobile-app-info .description {
    -webkit-padding-end: 8rem;
    padding-inline-end: 8rem;
  }
}
.mbile-app-card .mobile-app-info .mobile-app-links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 1rem;
}
.mbile-app-card .mobile-app-info .mobile-app-links .store-link img {
  height: 3.125rem;
}
.mbile-app-card .mobile-app-info .mobile-app-links .qr-code img {
  height: 3.125rem;
}
@media (max-width: 767.98px) {
  .mbile-app-card .mobile-app-info .mobile-app-links {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}
.mbile-app-card .mobile-img {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  /* margin-top: -6rem; */
}
.mbile-app-card .mobile-img img {
  max-width: 100%;
  max-height: 40rem;
  -o-object-fit: contain;
  object-fit: contain;
  /* -webkit-animation: floatY 8s ease-in-out infinite;
  animation: floatY 8s ease-in-out infinite; */
}

.nav-underline {
  --bs-nav-tabs-border-width: 0;
  --bs-nav-underline-gap: 1.5rem;
  --bs-nav-underline-border-width: 0.0125rem;
  --bs-nav-link-padding-x: 1.5rem;
  --bs-nav-link-padding-y: 1.5rem 3.5rem;
  --bs-nav-link-color: #fff;
  --bs-nav-tabs-border-color: #fff;
  --bs-nav-tabs-border-radius: 0;
  --bs-nav-link-hover-color: var(--clr-primary);
  --bs-nav-underline-link-active-color: var(--clr-primary);
}
.nav-underline .nav-link {
  font-family: "29LTBukra-Light";
  border-bottom-color: #fff;
  text-align: start;
}
.nav-underline .nav-link.active {
  font-family: "29LTBukra-Bold";
}

.tab-content .tab-pane {
  padding-block: 1.5rem;
  /* margin-top: -100px; */
}

.swiper-pagination {
  bottom: 0px !important;
  gap: 1rem;
}
.swiper-pagination .swiper-pagination-bullet {
  position: relative;
  width: 0.625rem;
  height: 0.625rem;
  padding: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  background-color: #586665;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  opacity: 1;
}
.swiper-pagination .swiper-pagination-bullet::after {
  position: absolute;
  inset-inline-start: -0.375rem;
  inset-block-start: -0.375rem;
  width: 1.375rem;
  height: 1.375rem;
  content: "";
  -ms-flex-negative: 0;
  flex-shrink: 0;
  background-color: transparent;
  border: 1.5px solid var(--clr-primary);
  opacity: 0;
  visibility: hidden;
  border-radius: 2rem;
}
.swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--clr-primary);
  opacity: 1;
}
.swiper-pagination .swiper-pagination-bullet-active::after {
  opacity: 1;
  visibility: visible;
}
.hero-slider .swiper-pagination {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  margin: 0;
  padding: 1rem 0;
  top: auto !important;
  bottom: auto !important;
}

.swiper-controls {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 1.5rem;
  gap: 0.5rem;
}
.swiper-controls .swiper-button-next,
.swiper-controls .swiper-button-prev {
  position: relative;
  inset: auto !important;
  margin: 0;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 2.5rem;
  width: 3.125rem;
  height: 3.125rem;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.05);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.swiper-controls .swiper-button-next::after,
.swiper-controls .swiper-button-prev::after {
  font-size: 1.5rem;
  color: #fff;
}
.swiper-controls .swiper-button-next:hover,
.swiper-controls .swiper-button-prev:hover {
  background-color: #fff;
}
.swiper-controls .swiper-button-next:hover::after,
.swiper-controls .swiper-button-prev:hover::after {
  color: var(--clr-primary);
}

.hero-img-slider {
  width: 100%;
}
.hero-img-slider .swiper {
  overflow: visible;
}
.hero-img-slider .swiper-slide {
  min-width: 18rem !important;
  height: 32rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: end;
  -webkit-box-align: self-start;
  -ms-flex-align: self-start;
  align-items: self-start;
  border-radius: 1rem !important;
  overflow: hidden;
}
@media (max-width: 1199.98px) {
  .hero-img-slider .swiper-slide {
    height: 24rem;
    min-width: none !important;
  }
}
.hero-img-slider .hero-img-card {
  position: relative;
  height: 100%;
  width: 100%;
}
.hero-img-slider .hero-img-card img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
.hero-img-slider .hero-img-card::after {
  position: absolute;
  inset: 0;
  content: "";
  background-color: rgba(0, 0, 0, 0.3);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.hero-img-slider .swiper-3d .swiper-slide-shadow,
.hero-img-slider .swiper-3d .swiper-slide-shadow-bottom,
.hero-img-slider .swiper-3d .swiper-slide-shadow-left,
.hero-img-slider .swiper-3d .swiper-slide-shadow-right,
.hero-img-slider .swiper-3d .swiper-slide-shadow-top {
  border-radius: 1rem !important;
}
.hero-img-slider .swiper-slide-active .hero-img-card {
  -webkit-transform: scale(1.05);
  -ms-transform: scale(1.05);
  transform: scale(1.05);
}
.hero-img-slider .swiper-slide-active .hero-img-card::after {
  opacity: 0;
  visibility: hidden;
}

.e-services-slider {
  width: 100%;
}
.e-services-slider .swiper-slide-active {
  z-index: 9;
}
.e-services-slider .swiper-pagination {
  position: relative;
  inset: 0 !important;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-block: 2rem 1rem;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

/* Home Vertical Swiper Pagination Styles - Modern Design */
.home-vertical-swiper .swiper-pagination {
  position: fixed !important;
  right: 25px !important;
  top: 50% !important;
  bottom: auto !important;
  left: auto !important;
  transform: translateY(-50%) !important;
  width: auto !important;
  height: auto !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 8px !important;
  z-index: 1000 !important;
  pointer-events: auto !important;
  padding: 15px 8px;
  background: rgba(37, 40, 41, 0.85);
  border-radius: 25px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Prevent dynamic bullets behavior and ensure all 6 bullets are always visible */
.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet-dynamic-main,
.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet-dynamic-prev,
.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet-dynamic-next,
.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet-dynamic-prev-prev,
.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet-dynamic-next-next {
  transform: none !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* Override any Swiper dynamic bullet hiding */
.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet[style*="display: none"] {
  display: block !important;
}

.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet[style*="opacity: 0"] {
  opacity: 1 !important;
}

/* Force show all 6 bullets */
.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet {
  position: relative;
  width: 4px;
  height: 20px;
  margin: 0 !important;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  opacity: 1 !important;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  border: none;
  overflow: hidden;
  display: block !important;
  visibility: visible !important;
}

/* Add slide number indicator */
.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet::after {
  content: attr(data-slide);
  position: absolute;
  right: -25px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  font-weight: 500;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
  white-space: nowrap;
  font-family: "Arial", sans-serif;
}

.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0%;
  background: linear-gradient(180deg, var(--clr-primary) 0%, #da7652 100%);
  border-radius: 2px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: bottom;
}

.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet:hover {
  background-color: rgba(255, 255, 255, 0.5);
  transform: scaleX(1.5);
  box-shadow: 0 0 8px rgba(180, 84, 52, 0.3);
}

.home-vertical-swiper
  .swiper-pagination
  .swiper-pagination-bullet:hover::after {
  opacity: 1;
  color: rgba(255, 255, 255, 0.9);
}

.home-vertical-swiper
  .swiper-pagination
  .swiper-pagination-bullet:hover::before {
  height: 30%;
}

.home-vertical-swiper .swiper-pagination .swiper-pagination-bullet-active {
  background-color: rgba(255, 255, 255, 0.6);
  transform: scaleX(2);
  box-shadow: 0 0 12px rgba(180, 84, 52, 0.5);
}

.home-vertical-swiper
  .swiper-pagination
  .swiper-pagination-bullet-active::after {
  opacity: 1;
  color: #fff;
  font-weight: 600;
}

.home-vertical-swiper
  .swiper-pagination
  .swiper-pagination-bullet-active::before {
  height: 100%;
  animation: slide-up 0.6s ease-out;
}

/* Slide up animation for active bullet */
@keyframes slide-up {
  0% {
    height: 0%;
    transform: scaleY(0);
  }
  50% {
    transform: scaleY(1.1);
  }
  100% {
    height: 100%;
    transform: scaleY(1);
  }
}

/* RTL Support for Home Vertical Swiper */
[dir="rtl"] .home-vertical-swiper .swiper-pagination {
  right: auto !important;
  left: 25px !important;
}

[dir="rtl"]
  .home-vertical-swiper
  .swiper-pagination
  .swiper-pagination-bullet::after {
  right: auto;
  left: -25px;
}

/* Responsive Design for Home Vertical Swiper Pagination */
@media (max-width: 1200px) {
  .home-vertical-swiper .swiper-pagination {
    right: 20px !important;
    padding: 12px 6px;
  }

  [dir="rtl"] .home-vertical-swiper .swiper-pagination {
    left: 20px !important;
  }
}

@media (max-width: 768px) {
  .home-vertical-swiper .swiper-pagination {
    right: 15px !important;
    gap: 6px !important;
    padding: 10px 5px;
    border-radius: 20px;
    transform: translateY(-50%) scale(0.9) !important;
  }

  [dir="rtl"] .home-vertical-swiper .swiper-pagination {
    left: 15px !important;
  }

  .home-vertical-swiper .swiper-pagination .swiper-pagination-bullet {
    width: 3px;
    height: 16px;
  }
}

@media (max-width: 576px) {
  .home-vertical-swiper .swiper-pagination {
    right: 10px !important;
    gap: 5px !important;
    padding: 8px 4px;
    border-radius: 15px;
    transform: translateY(-50%) scale(0.85) !important;
  }

  [dir="rtl"] .home-vertical-swiper .swiper-pagination {
    left: 10px !important;
  }

  .home-vertical-swiper .swiper-pagination .swiper-pagination-bullet {
    width: 2px;
    height: 14px;
  }

  .home-vertical-swiper .swiper-pagination .swiper-pagination-bullet-active {
    transform: scaleX(1.5);
  }

  .home-vertical-swiper .swiper-pagination .swiper-pagination-bullet:hover {
    transform: scaleX(1.2);
  }
}

.hero {
  position: relative;
  background-color: var(--clr-secondary);
  height: 120vh;
  min-height: 50rem;
  padding: 0;
}
.hero::before {
  position: absolute;
  inset-inline: 0;
  inset-block-end: 0;
  content: "";
  z-index: 2;
  background: -webkit-gradient(
    linear,
    left bottom,
    left top,
    from(#252829),
    color-stop(rgba(37, 40, 41, 0.85)),
    to(rgba(37, 40, 41, 0))
  );
  background: linear-gradient(
    to top,
    #252829,
    rgba(37, 40, 41, 0.85),
    rgba(37, 40, 41, 0)
  );
  height: 12.5rem;
}
.hero .bg {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  width: 100%;
  z-index: 1;
  background-color: var(--clr-secondary);
}
.hero .bg::after {
  position: absolute;
  inset: 0;
  content: "";
  z-index: 2;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(rgba(38, 41, 42, 0.6)),
    color-stop(23.56%, rgba(21, 22, 23, 0.5)),
    to(rgba(42, 45, 46, 0.3))
  );
  background: linear-gradient(
    180deg,
    rgba(38, 41, 42, 0.6) 0%,
    rgba(21, 22, 23, 0.5) 23.56%,
    rgba(42, 45, 46, 0.3) 100%
  );
  -webkit-filter: blur(25px);
  filter: blur(25px);
}
.hero .bg img {
  -webkit-animation: pulse 16s ease-in-out infinite;
  animation: pulse 16s ease-in-out infinite;
}
.hero .bg video,
.hero .bg img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center;
  object-position: center;
  z-index: 0;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}
.hero .hero-caption {
  position: absolute;
  inset: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100%;
  z-index: 2;
}
@media (max-width: 991.98px) {
  .hero .hero-caption {
    padding-top: 12rem;
  }
}
.hero .swiper-pagination {
  -webkit-margin-start: 0.125rem;
  margin-inline-start: 0.125rem;
}

.ncw-map {
  position: relative;
  z-index: 10;
  background-color: var(--clr-secondary);
  overflow: hidden;
}
.ncw-map .container-fluid {
  position: relative;
  z-index: 2;
}
.ncw-map::before {
  position: absolute;
  inset-inline: 0;
  inset-block-start: 0;
  content: "";
  z-index: 0;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(#252829),
    color-stop(rgba(37, 40, 41, 0.85)),
    to(rgba(37, 40, 41, 0))
  );
  background: linear-gradient(
    to bottom,
    #252829,
    rgba(37, 40, 41, 0.85),
    rgba(37, 40, 41, 0)
  );
  height: 12.5rem;
}
.ncw-map::after {
  position: absolute;
  inset-inline: 0;
  inset-block-end: 0;
  content: "";
  background: -webkit-gradient(
    linear,
    left bottom,
    left top,
    from(#252829),
    color-stop(rgba(37, 40, 41, 0.85)),
    to(rgba(37, 40, 41, 0))
  );
  background: linear-gradient(
    to top,
    #252829,
    rgba(37, 40, 41, 0.85),
    rgba(37, 40, 41, 0)
  );
  height: 12.5rem;
  z-index: 0;
}
.ncw-map .bg {
  position: absolute;
  inset: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  /* background-color: var(--clr-secondary); */
  background-color: rgb(37 40 41 / 80%);
  overflow: hidden;
}
.ncw-map .bg img {
  -webkit-animation: pulse 8s ease-in-out infinite;
  animation: pulse 8s ease-in-out infinite;
}
.ncw-map .bg video,
.ncw-map .bg img {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center;
  object-position: center;
  z-index: 0;
  opacity: 0;
  mix-blend-mode: multiply;
  -webkit-transition: opacity 0.5s ease;
  transition: opacity 0.5s ease;
}
.ncw-map .bg video.active,
.ncw-map .bg img.active {
  opacity: 0.5;
  z-index: 1;
}
.ncw-map .ncwMap-tabs {
  margin-bottom: 4rem;
}
@media (min-width: 992px) {
  .ncw-map .ncwMap-tabs {
    -webkit-margin-end: 5rem;
    margin-inline-end: 5rem;
    margin-bottom: 8rem;
  }
}
.ncw-map .map-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}
/* @media (min-width: 1200px) {
  .ncw-map .map-content {
    -webkit-margin-start: 4rem;
    margin-inline-start: 4rem;
  }
} */
.ncw-map .map-content img,
.ncw-map .map-content svg {
  height: 34.375rem;
  max-width: 100%;
  -o-object-fit: contain;
  object-fit: contain;
}
@media (max-width: 1199.98px) {
  .ncw-map .map-content img,
  .ncw-map .map-content svg {
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
    height: 25rem;
  }
}
@media (max-width: 991.98px) {
  .ncw-map .map-content img,
  .ncw-map .map-content svg {
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
    height: 18.75rem;
  }
}
.ncw-map .map-content svg path {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.ncw-map .map-content svg path:hover {
  fill: var(--clr-primary);
  stroke: var(--clr-primary);
}

.e-services {
  position: relative;
  background-color: #fff;
  overflow: hidden;
  z-index: 1;
}
.e-services .bg {
  position: absolute;
  inset: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  overflow: hidden;
}
.e-services .bg img {
  -webkit-animation: pulse 24s ease-in-out infinite;
  animation: pulse 24s ease-in-out infinite;
}
.e-services .bg video,
.e-services .bg img {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center;
  object-position: center;
  z-index: 0;
  opacity: 0.5;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.portal-statistics {
  position: relative;
  overflow: hidden;
  background: rgba(224, 195, 171, 0.6);
  z-index: 1;
  height: 100vh;
}
.portal-statistics.padding-block {
  padding: 6rem 0;
}
.portal-statistics::before {
  position: absolute;
  inset: 0;
  content: "";
  z-index: 0;
  /* background: -webkit-gradient(
    linear,
    right top,
    left top,
    color-stop(0.1%, #fff),
    color-stop(99.87%, rgba(255, 255, 255, 0))
  );
  background: linear-gradient(270deg, #fff 0.1%, rgba(255, 255, 255, 0) 99.87%); */
}
[dir="ltr"] .portal-statistics::before {
  -webkit-transform: scale(-1);
  -ms-transform: scale(-1);
  transform: scale(-1);
}
.portal-statistics .bg {
  position: absolute;
  inset: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  overflow: hidden;
}
[dir="ltr"] .portal-statistics .bg img {
  -webkit-animation: none;
  animation: none;
  -webkit-transform: rotateY(180deg) !important;
  transform: rotateY(180deg) !important;
}
.portal-statistics .bg video,
.portal-statistics .bg img {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center;
  object-position: center;
  z-index: 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.tippy-box {
  background-color: var(--clr-primary) !important;
}
.tippy-arrow {
  color: var(--clr-primary) !important;
}

/* start breadcrumb */

.breadcrumb {
  height: 355px;
  position: relative;
}

.breadcrumb img.bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.breadcrumb .content {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 10px;
}

.breadcrumb .content svg,
.breadcrumb .content a {
  color: #fff;
  margin-top: 8px;
}

.breadcrumb h2 {
  color: #fff;
  font-size: 28px;
  margin: 0;
}
/* end breadcrumb */

.card-title {
  color: var(--clr-primary);
  font-size: 18px;
  margin-bottom: 30px;
}

.services-list {
  padding-bottom: 100px;
}

.services-list .services-card {
  border: 1px solid #ddd;
  box-shadow: none;
  cursor: pointer;
}

.section-list {
  /* chnage bullet color */
  list-style: none;
  padding: 0;
}
.section-list li {
  position: relative;
  padding-inline-start: 20px;
  margin-block: 20px;
}
.section-list li::before {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  /* top: 50%;
  transform: translateY(-50%); */
  top: 15px;
  width: 10px;
  height: 10px;
  background-color: var(--clr-primary);
  border-radius: 50%;
}

.explorer-list {
  margin-bottom: 3rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 30px;
}
.explorer-card {
  height: 160px;
}
.explorer-card,
.download-card {
  background: #fff;
  border: 1px solid #eee;
  padding: 15px;
  border-radius: 5px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 15px;
}

.explorer-card img {
  width: 40px;
  height: 40px;
}

.explorer-card span,
.download-card span,
.guide-item-name {
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 1) 0%,
    rgba(180, 84, 52, 1) 50%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text; /* For Firefox */
  color: transparent; /* Fallback */
  /* font-size: 18px; */
}

.download-card {
  border-radius: 20px;
  padding: 25px 40px;
  border: 1px solid #b9b9b9;
}

/* .download-card {
  font-size: 18px;
} */

.download-card a {
  display: block;
  width: 100%;
  margin-top: 20px;
}

.download-card div {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-block: 20px;
}

.download-card img {
  /* width: 30px;
  height: 30px;
  cursor: pointer; */
}

/* management_tasks */
.management_tasks {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 50px;
}
.management_tasks .task {
  display: flex;
  gap: 10px;
  align-items: center;
  background-color: #fff;
  /* border: 1px solid #ddd; */
  padding: 10px 0;
  border-radius: 45px;
}
.management_tasks .task:nth-child(odd) {
  padding-inline-end: 200px;
}
.management_tasks .task:nth-child(even) {
  padding-inline-start: 235px;
}
.management_tasks .task:nth-child(10) {
  padding-inline-start: 100px;
}
.management_tasks .task:nth-child(8) {
  padding-inline-start: 187px;
}
.management_tasks .task:nth-child(2) {
  padding-inline-start: 187px;
}
.management_tasks .task h5 {
  color: #fff;
  background-color: var(--clr-primary);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transform: translateX(40%);
  flex-shrink: 0;
}
.management_tasks .task p {
  margin: 0;
  line-height: 30px;
}
.task-image {
  position: absolute;
  top: -50px;
  right: 50%;
  transform: translateX(50%);
}
/* management_tasks */

/* guide tabs */
.tabs {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 50px;
  margin-block: 50px;
  width: fit-content;
}
.tabs .tab {
  width: 300px;
  text-align: center;
  padding: 20px;
  border-radius: 50px;
  cursor: pointer;
  font-size: 20px;
}
.tabs .tab.active {
  background-color: var(--clr-primary);
  color: #fff;
}

.guides-list {
  display: flex;
  gap: 30px;
  margin-block: 30px;
  justify-content: center;
  /* overflow: auto; */
  flex-wrap: wrap;
}
.guide-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 10px;
  margin: 2px;
}

.guide-item.active .guide-item-content {
  background-color: #fff;
  outline: 2px dashed var(--clr-primary);
}

.guide-item-content {
  width: 84px;
  height: 84px;
  background-color: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
}
.guide-item img {
  width: 56px;
  height: 56px;
}
.guide-item-name {
  font-size: 14px;
}
/* guide tabs */

.grid {
  /* background: #ddd; */
}

/* clear fix */
.grid:after {
  content: "";
  display: block;
  clear: both;
}

/* ---- .grid-item ---- */

.grid-item {
  width: 33.333%;
}

.grid-item {
  float: left;
  position: relative;
  padding: 10px;
  cursor: pointer;
}

.grid-item img {
  display: block;
  max-width: 100%;
  border-radius: 10px;
  /* padding: 10px; */
}

.grid-item .overlay {
  position: absolute;
  bottom: 20px;
  right: 20px;
  color: #fff;
  font-size: 15px;
  width: 80%;
}

.search-input {
  width: 400px;
  padding: 12px 20px;
  margin: 8px 0;
  box-sizing: border-box;
  border: 1px solid #ddd;
  outline: none;
  border-radius: 30px;
  font-size: 16px;
  background-color: white;
  background-position: 10px 10px;
  background-repeat: no-repeat;
  padding-left: 40px;
  -webkit-transition: width 0.4s ease-in-out;
  transition: width 0.4s ease-in-out;
}

.search-container {
  position: relative;
}

.search-container svg {
  position: absolute;
  width: 25px;
  height: 25px;
  top: 50%;
  right: 359px;
  transform: translateY(-41%);
  color: var(--clr-primary);
}

.modal-backdrop {
  z-index: 0 !important;
}

.modal-content {
  border-radius: 15px;
  overflow: hidden;
}

.modal-header {
  background-color: var(--clr-primary);
}

.modal-header .btn-close {
  color: #fff;
  background-color: transparent;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-size: 8px;
  border: 1.5px solid #fff;
  opacity: 1;
  filter: brightness(0) saturate(100%) invert(100%) sepia(2%) saturate(8%)
    hue-rotate(181deg) brightness(103%) contrast(102%);
}

/* === Gallery item height/width ================================================= */
.gallery-item > div {
  height: 200px !important; /* !h-[200px]  */
  width: 100% !important; /* !w-full     */
}

/* === Gallery item iframe rounding ============================================= */
.gallery-item > div iframe {
  border-radius: 0.75rem; /* rounded‑xl  (12 px in Tailwind's default scale) */
}

/* === Image‑gallery slides ====================================================== */
.image-gallery-slide > div,
.image-gallery-slide > img {
  height: 200px !important; /* !h-[300px]  */
  width: auto !important; /* !w-full     */
  border-radius: 0.75rem !important; /* !rounded-xl */
  object-fit: cover !important; /* !object-cover */
}

/* === Full‑screen mode override ================================================= */
.image-gallery-content.fullscreen .image-gallery-slide > img,
.image-gallery-content.fullscreen .image-gallery-slide > div {
  height: calc(100vh - 150px) !important;
}

/* === Thumbnail buttons ========================================================= */
.image-gallery-thumbnails-container button {
  flex: 1 1 0%; /* flex-1  (flex‑grow:1, flex‑shrink:1, flex‑basis:0) */
  border-radius: 0.5rem; /* rounded-lg (8 px) */
}

/* === Thumbnail button images =================================================== */
.image-gallery-thumbnails-container button img {
  border-radius: 0.375rem; /* rounded-md (6 px) */
}

/* === Thumbnail image size/fit ================================================== */
.image-gallery-thumbnail-image {
  height: 70px; /* h-[70px] */
  object-fit: cover; /* object-cover */
}

/* === Left / right navigation arrow icons ====================================== */
.image-gallery-left-nav .image-gallery-svg,
.image-gallery-right-nav .image-gallery-svg {
  height: 4rem !important; /* !h-16  (64 px) */
  width: 4rem !important; /* !w-16  (64 px) */
}

/* === Hover state for navigation icons ========================================= */
.image-gallery-icon:hover {
  color: var(--main_color_light) !important; /* !text-main_color_light */
}

.image-gallery {
  margin-block: 15px;
}

.chart-logo {
  width: 50px !important;
  height: 50px !important;
}

.tasks-image {
  width: 470px;
  height: 470px;
  object-fit: cover;
  border-radius: 50%;
}

.one-guide-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}
.one-guide-list img {
  width: 100%;
  height: 200px;
  /* object-fit: cover; */
  object-fit: contain;
  border-radius: 10px;
}

.one-guide-list .one-guide-item {
  border: 1px solid #ddd;
  padding: 10px;
  border-radius: 10px;
}

.guide-img {
  cursor: pointer;
}

.image-gallery-image img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 10px;
}

.more-btn {
  background-color: var(--clr-primary);
  color: #fff;
  border-radius: 10px;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 20px auto;
  display: block;
  width: 200px;
  text-align: center;
}
.more-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.map {
  position: absolute;
  inset: 0;
  z-index: -1;
}

.map img {
  width: 100% !important;
  height: 100% !important;
}

.app-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background: #fff;
  border-radius: 1rem;
  box-shadow: 0 4px 24px rgba(180, 84, 52, 0.1);
  padding: 2.5rem 1.5rem 2rem 1.5rem;
  /* min-height: 320px; */
  transition: box-shadow 0.2s, transform 0.2s;
  border: 1px solid #f2f2f2;
  text-align: center;
  width: 100%;
}
.app-card:hover {
  box-shadow: 0 8px 32px rgba(180, 84, 52, 0.18);
  transform: translateY(-6px) scale(1.03);
  border-color: #b45434;
}
.app-card .app-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #f7f7f7;
  margin-bottom: 1.5rem;
}
.app-card .app-icon img {
  width: 48px;
  height: 48px;
  object-fit: contain;
}
.app-card .app-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #b45434;
  margin-bottom: 0.75rem;
}
.app-card .app-description {
  font-size: 1rem;
  color: #888;
  line-height: 1.6;
}
