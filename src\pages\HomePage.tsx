import Hero from "../components/home/<USER>";
import Services from "../components/home/<USER>";
import PortalStatistics from "../components/home/<USER>";
import { MapProvider } from "../components/mapFolder/MapContext.js";
import { useEffect, useRef, useState } from "react";
import { scrollToTop } from "../utils/helpers";
import Map2 from "../components/home/<USER>/Map2.js";

import ContactUsSection from "../components/home/<USER>";
import FooterLinksSection from "../components/home/<USER>";
import MobileMenu from "../components/home/<USER>";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Mousewheel, Keyboard } from "swiper/modules";
import Navbar from "../components/Navbar.js";

/**
 * HomePage component with vertical carousel
 * This page includes the Hero, Map, Services, Statistics, Contact Us, and Footer Links sections
 */
export default function HomePage() {
  const swiperRef = useRef<any>(null);
  const [activeSlide, setActiveSlide] = useState(0);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  console.log(activeSlide);

  // Scroll to top when the component mounts
  useEffect(() => {
    scrollToTop();

    // Add carousel-active class to body
    document.body.classList.add("carousel-active");

    return () => {
      // Remove carousel-active class when component unmounts
      document.body.classList.remove("carousel-active");

      // Reset navbar to default state when leaving home page
      const header = document.querySelector(".main-header");
      if (header) {
        header.classList.remove("scrolled");
      }
    };
  }, []);

  // Handle slide change
  const handleSlideChange = (swiper: any) => {
    const newActiveSlide = swiper.activeIndex;
    setActiveSlide(newActiveSlide);

    // Handle navbar styling based on active slide
    const header = document.querySelector(".main-header");
    if (header) {
      // Add scrolled class for sections with light backgrounds
      // Services section (index 2) has white background
      // Statistics section (index 3) has light beige background
      if (newActiveSlide === 2 || newActiveSlide === 3) {
        header.classList.add("scrolled");
      } else {
        header.classList.remove("scrolled");
      }
    }
  };

  return (
    <>
      <Navbar onMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)} />

      {/* Vertical Carousel */}
      <Swiper
        ref={swiperRef}
        direction="vertical"
        slidesPerView={1}
        spaceBetween={0}
        mousewheel={{
          thresholdDelta: 50,
          sensitivity: 1,
        }}
        keyboard={{
          enabled: true,
          onlyInViewport: true,
        }}
        pagination={{
          clickable: true,
          bulletClass: "swiper-pagination-bullet",
          bulletActiveClass: "swiper-pagination-bullet-active",
        }}
        speed={1000}
        modules={[Pagination, Mousewheel, Keyboard]}
        className="home-vertical-swiper"
        onSlideChange={handleSlideChange}
        style={{ height: "100vh" }}
      >
        <SwiperSlide id="hero-section">
          <Hero />
        </SwiperSlide>

        <SwiperSlide id="map-section">
          <MapProvider>
            <Map2 />
          </MapProvider>
        </SwiperSlide>

        <SwiperSlide id="services-section">
          <Services />
        </SwiperSlide>

        <SwiperSlide id="statistics-section">
          <PortalStatistics />
        </SwiperSlide>

        <SwiperSlide id="contact-us-section">
          <ContactUsSection />
        </SwiperSlide>

        <SwiperSlide id="footer-links-section">
          <FooterLinksSection />
        </SwiperSlide>
      </Swiper>

      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />
    </>
  );
}
